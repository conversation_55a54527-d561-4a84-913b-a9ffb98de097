from fastapi import APIRouter, HTTPException, status, Response, BackgroundTasks
from typing import List, Optional
from fastapi.responses import JSONResponse
from bson import ObjectId
import json
from src.common.handlers import pydantic_json_encoder
from src.repositories.contactrepository import ContactRepository, ContactRepositoryError
from src.services.backgroundservice import BackgroundTaskService
from src.models.contactmodels import ContactDetail


router = APIRouter(
    prefix="/contact",
    tags=["Contact"],
    responses={404: {"description": "Not found"}}
)

contact_repository = ContactRepository()
bg_service = BackgroundTaskService()

@router.get("/scrape", status_code=status.HTTP_200_OK)
async def scrape_contact(bg_tasks: BackgroundTasks, id: Optional[str] = None, url: Optional[str] = None):
    try:
        def scrape_task():
            laa_id = ObjectId(id) if id else None
            return contact_repository.scrape(laa_id, url)

        task_id = bg_service.add_background_task(
            background_tasks=bg_tasks,
            func=scrape_task
        )

        metadata = {}
        if id:
            metadata["laa_id"] = ObjectId(id)
        if url:
            metadata["laa_url"] = url

        bg_service.update_task(
            task_id,
            metadata=metadata
        )

        task = bg_service.get_task(task_id)
        return JSONResponse(content=json.loads(json.dumps(task, default=pydantic_json_encoder)))

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ContactRepositoryError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("", response_model=ContactDetail, status_code=status.HTTP_200_OK)
async def get_contact(id: Optional[str] = None, url: Optional[str] = None):
    try:
        contact = contact_repository.get_by_laa(ObjectId(id), url)
        if not contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact not found"
            )
        return contact
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid ID format"
        )
    except ContactRepositoryError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
