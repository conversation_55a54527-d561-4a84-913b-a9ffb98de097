from src.mongocontext.mongocontext import MongoContext
from src.models.contactmodels import ContactDetail
from typing import Optional
from bson import ObjectId
from src.models.laamodels import LAAgency
from src.repositories.laarepository import LAARepository
from src.services.extractorservice import ExtractContactDetails
from datetime import datetime, UTC, timedelta
from dataclasses import asdict
from src.appsettings import CONTACT_REFRESH_DAYS
from pymongo.errors import (
    OperationFailure,
    ConnectionFailure,
    ServerSelectionTimeoutError
)


class ContactRepositoryError(Exception):
    """Base exception for ContactRepository errors"""
    pass

class ContactRepository:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ContactRepository, cls).__new__(cls)
            # Initialize instance variables but not connections
            cls._instance._extractor = None
            cls._instance._laa_repo = None
            cls._instance._mongo_context = None
            cls._instance._collection = None
        return cls._instance
    
    def __init__(self):
        # Constructor does nothing - initialization happens in __new__
        pass
    
    @property
    def extractor(self):
        if self._extractor is None:
            self._extractor = ExtractContactDetails()
        return self._extractor
    
    @property
    def laa_repo(self):
        if self._laa_repo is None:
            self._laa_repo = LAARepository()
        return self._laa_repo
    
    @property
    def mongo_context(self):
        if self._mongo_context is None:
            self._mongo_context = MongoContext()
        return self._mongo_context
    
    @property
    def collection(self):
        if self._collection is None:
            try:
                self._collection = self.mongo_context.get_collection("contacts")
            except (ConnectionFailure, ServerSelectionTimeoutError) as e:
                raise ContactRepositoryError(f"Failed to initialize repository: {str(e)}")
        return self._collection

    def get_contacts(self):
        try:
            results = self.collection.find()
            return [ContactDetail(**result) for result in results]
        except OperationFailure as e:
            raise ContactRepositoryError(f"Failed to retrieve contacts: {str(e)}")

    def scrape(self, laa_id: Optional[ObjectId] = None, laa_url: Optional[str] = None, force: bool = False) -> Optional[ContactDetail]:
        if not laa_id and not laa_url:
            raise ValueError("Either laa_id or laa_url must be provided")
        laa = self.laa_repo.get_by_id(laa_id) if laa_id else self.laa_repo.get_by_website(laa_url)
        if not laa:
            raise ContactRepositoryError("No LAA found with the specified ID or URL")
        contact = self.get_by_laa(laa_id, laa_url)
        if (contact and not contact.SystemUpdated and not force and
                datetime.now(UTC).replace(tzinfo=None) < contact.DateModified + timedelta(days=CONTACT_REFRESH_DAYS)):
            return contact
        contact = self.extractor.extract_contact_detail(laa)
        return self.upsert_contact(laa, contact)

    def update_contact(self, laa_id: ObjectId, contact: ContactDetail):
        laa = self.laa_repo.get_by_id(laa_id)
        if not laa:
            raise ContactRepositoryError("No LAA found with the specified ID")
        if not contact.ETag:
            raise ValueError("ETag is required for updates")
        try:
            original = self.collection.find_one({"LAAId": laa_id})
            if not original:
                raise ContactRepositoryError("No contact found with the specified ID")
            if original.get('ETag') != contact.ETag:
                raise ContactRepositoryError("Concurrency conflict: Contact was modified by another user")
            contact.SystemUpdated = False
            contact.ContactPerson = original['ContactPerson']
            contact.generate_etag()
            contact.DateCreated = original['DateCreated']
            contact.DateModified = datetime.now(UTC)
            result = self.collection.update_one(
                {"_id": original._id},
                {"$set": asdict(contact)}
            )
            return contact
        except OperationFailure as e:
            raise ContactRepositoryError(f"Failed to update contact: {str(e)}")

    def upsert_contact(self, laa: LAAgency, contact: Optional[ContactDetail] = None) -> ContactDetail:
        if not contact:
            contact = ContactDetail(AgencyName=laa.LAAName, AgencyUrl=laa.Website, InformationPending=True)
        contact.SystemUpdated = True
        contact.LAAId = laa._id
        contact.AgencyName = laa.LAAName
        contact.AgencyUrl = laa.Website
        contact.generate_etag()
        contact.DateModified = datetime.now(UTC)
        try:
            original = self.collection.find_one({"LAAId": laa._id})
            if original:
                contact._id = original['_id']
                contact.DateCreated = original['DateCreated']
                contact.SystemUpdated = True
                self.collection.update_one(
                    {"_id": contact._id},
                    {"$set": asdict(contact)}
                )
                return contact
            result = self.collection.insert_one(asdict(contact))
            contact._id = result.inserted_id
            return contact
        except Exception as e:
            raise ContactRepositoryError(f"Failed to upsert contact: {str(e)}")

    def get_by_laa(self, laa_id: Optional[ObjectId] = None, laa_url: Optional[str] = None) -> Optional[ContactDetail]:
        try:
            if laa_id:
                result = self.collection.find_one({"LAAId": laa_id})
                return ContactDetail(**result) if result else None
            result = self.collection.find_one({'AgencyUrl': laa_url})
            return ContactDetail(**result) if result else None
        except OperationFailure as e:
            raise ContactRepositoryError(f"Database operation failed: {str(e)}")
        except Exception as e:
            print(e)
            raise