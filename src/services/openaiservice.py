from src.common.handlers import pydantic_json_encoder
from src.models.contactmodels import *
import openai
import json
import re
import os
from dataclasses import asdict
from typing import List, Dict, Any, Optional
from pydantic import ValidationError


class OpenAIService:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(OpenAIService, cls).__new__(cls)
            # Initialize instance variables but not connections
            cls._instance._client = None
            cls._instance.target_schema = json.dumps({
                "AgencyName": "string (use provided)",
                "AgencyUrl": "string (use provided)",
                "Jurisdiction": "string (use provided)",
                "OrganizationType": "string (use provided)",
                "Email": "list[string] (valid emails, unique)",
                "Phone": "list[integer] (valid phone numbers, unique)",
                "Address": "list[string] (valid PO Box or street addresses, unique)",
                "Socials": "list[string] (valid social media URLs, unique)",
                "ContactPerson": [
                    {
                        "ContactName": "string (full name)",
                        "ContactRole": "string | null (job or role title)",
                        "Email": "list[string] (valid emails, unique)",
                        "Phone": "list[integer] (valid phone numbers, unique)",
                        "Address": "list[string] (valid PO Box or street addresses, unique)",
                        "Socials": "list[string] (valid social media URLs, unique)"
                    }
                ]
            }, indent=2)
            cls._instance.social_keywords = ["facebook.com", "x.com", "twitter.com", "instagram.com", "linkedin.com"]
        return cls._instance
    
    def __init__(self):
        # Constructor does nothing - initialization happens in __new__
        pass
    
    @property
    def client(self):
        if self._client is None:
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("Missing OpenAI API Key. Ensure it's set in the .env file.")
            self._client = openai.OpenAI(api_key=api_key)
        return self._client

    def fetch_contact_info(self, company, url, html) -> List[Dict[str, Any]]:
        system_message = {
            "role": "system",
            "content": (
                "You are a strict JSON extractor. Follow the instructions exactly and output only a valid JSON list with no extra text."
            )
        }
        user_message = {
            "role": "user",
            "content": f"""Extract all available contact details from the HTML text related to {company} at {url}.
            Return a JSON list of flat objects. Each object must include:
            - "type": one of ["email", "phone", "address", "name", "role", "socials"]
            - "value": the extracted information (for phone numbers, include only digits)
            - "person_name": optional; include if the detail is clearly tied to a specific person.
    
            Rules:
            1. Extract all identifiable contact information.
            2. Every person with a name must have a corresponding "role" object if available.
            3. Always associate roles (job titles) with the corresponding person’s name by creating separate "name" and "role" objects linked by "person_name".
            4. If an email or phone number is tied to an individual, include "person_name"; omit it for general contact info.
            5. Return phone numbers as strings containing only digits (no spaces, dashes, or parentheses).
            6. Addresses may include street addresses, PO Box numbers, or other identifiable location descriptions.
            7. For "socials", extract only URLs for Facebook, Twitter/X, Instagram, and LinkedIn. Each URL should be in its own object with "type": "socials" and include "person_name" if applicable.
            8. Output ONLY the JSON list without any additional text or explanation.
            9. If no contact information is found, output [].
    
            HTML:
            ```html
            {html}
            ```"""
        }

        try:
            completion = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[system_message, user_message],
                temperature=0,
                stop=["\n\n"],
                response_format={"type": "json_object"} # Note: Expecting a dict containing the list
            )

            # Assuming the response format might wrap the list in a key, e.g., {"contacts": [...]}
            # Or it might return the list directly if the model understands the prompt well.
            response_content = completion.choices[0].message.content.strip()
            if not response_content:
                print("⚠️ OpenAI returned an empty response.")
                return []

            extracted_data = json.loads(response_content)

            # Handle potential wrapping dictionary { "key": [...] }
            if isinstance(extracted_data, dict):
                # Try to find a key whose value is a list
                list_value = next((v for v in extracted_data.values() if isinstance(v, list)), None)
                if list_value is not None:
                    extracted_items = list_value
                else:
                    print(f"⚠️ OpenAI returned a dict, but no list found inside: {extracted_data}")
                    return []
            elif isinstance(extracted_data, list):
                extracted_items = extracted_data
            else:
                print(f"⚠️ OpenAI returned unexpected format: {type(extracted_data)}")
                return []

            # Basic validation of list items
            validated_items = []
            for item in extracted_items:
                if isinstance(item, dict) and 'type' in item and 'value' in item:
                    validated_items.append(item)
                else:
                     print(f"⚠️ Skipping invalid item in response: {item}")
            
            return validated_items

        except json.JSONDecodeError:
            print(f"⚠️ JSON Parsing Error! Raw Response: {response_content}")
            return []
        except openai.APIError as e:
            print(f"❌ OpenAI API Error: {e}")
            return []
        except Exception as e:
            print(f"❌ Unexpected Error processing OpenAI response: {e}")
            print(f"Raw response causing error: {response_content if 'response_content' in locals() else 'N/A'}")
            return []

    def classify_agency(self, company: str, url: str) -> List[Dict[str, Any]]:
        system_message = {
            "role": "system",
            "content": """
                You are the ‘Local Arts Agency Classifier’. Your job is to assign exactly two labels to a single organization:
                    1. jurisdiction ∈ {City, County / Regional, Other}  
                    2. organization_type ∈ {Government, Non-profit, Other}
    
                You **must** invoke the Web Search tool and:
                  • Navigate to the agency’s “Home”, “Main”, “Index”, “About”, “Mission”, or equivalent page or section to find its **service-area wording**.  
                  • **Do not** use contact addresses, office locations, or mailing addresses to decide jurisdiction.
    
                Apply the following classification logic:
    
                — JURISDICTION —  
                1. If the service-scope or mission text contains any of:
                   - “County” (e.g. “Dallas County”, “county-wide”, “multi-county”)
                   - “region” / “regional” (e.g. “regional arts coalition”, “in the region”, "serves the entire region”, "serves the X region")
                   - “metro” (e.g. “metro area”, “metropolitan region”)
                   - “greater X” (e.g. “Greater Birmingham”, “Greater Selma Area”)
                   - Any phrase like “Gulf Coast area”, “Tri-State area”, or multi-town references
                   - Any wording like “only agency in the region” or “across multiple towns”
                   - Any phrases like "educate, encourage, and nurture the River Region" or "serves the X region"
                   → **County / Regional**
                2. Else if it explicitly states “City of X”, “Town of X”, or clearly limits scope to one municipality → **City**
                3. Else → **Other**
    
                — ORGANIZATION_TYPE —  
                1. If the domain ends in “.gov” or it uses language like “Department of…”, “Office of…”, “Commission” → **Government**
                2. Else if it contains nonprofit indicators (e.g. “501(c)(3)”, “nonprofit”, “charitable”, “board of directors”, “fundraising”, “membership”) → **Non-profit**
                3. Else → **Other**
    
                **Fallback**: if evidence is missing, choose **Other** (jurisdiction) or **Other** (organization_type).
    
                For each label, include:
                - `"type"`: "jurisdiction" or "organization_type"
                - `"value"`: one of the accepted label values
                - `"reason"`: quote the phrase and link you used to decide
    
                Return ONLY the JSON array:
                ```json
                [
                  { "type": "jurisdiction", "value": "...", "reason": "..." },
                  { "type": "organization_type", "value": "...", "reason": "..." }
                ]
                ```
                """
        }

        user_message = {
            "role": "user",
            "content": f"""
                Classify this organization:
                
                • Name: {company}
                • URL:  {url}
                
                Invoke the Web Search tool, extract the service-area wording, then apply the above rules in order. Return only the JSON list as specified.
                """
        }

        try:
            completion = self.client.chat.completions.create(
                model="gpt-4o-mini-search-preview",
                messages=[system_message, user_message],
                web_search_options={
                    "search_context_size": "high",
                    "user_location": {
                        "type": "approximate",
                        "approximate": {
                            "country": "US"
                        }
                    }
                }
            )

            response_content = completion.choices[0].message.content.strip()
            if not response_content:
                print("⚠️ OpenAI returned an empty response.")
                return []

            extracted_data = None

            if response_content.startswith("```json") and "```" in response_content[7:]:
                json_content = response_content.split("```json", 1)[1].split("```")[0].strip()
                extracted_data = json.loads(json_content)
            else:
                extracted_data = json.loads(response_content)

            # Handle potential wrapping dictionary { "key": [...] }
            if isinstance(extracted_data, dict):
                # Try to find a key whose value is a list
                list_value = next((v for v in extracted_data.values() if isinstance(v, list)), None)
                if list_value is not None:
                    extracted_items = list_value
                else:
                    print(f"⚠️ OpenAI returned a dict, but no list found inside: {extracted_data}")
                    return []
            elif isinstance(extracted_data, list):
                extracted_items = extracted_data
            else:
                print(f"⚠️ OpenAI returned unexpected format: {type(extracted_data)}")
                return []

            # Basic validation of list items
            validated_items = []
            for item in extracted_items:
                if not isinstance(item, dict) or "type" not in item or "value" not in item:
                    print(f"⚠️ Skipping invalid item: {item}")
                    continue

                # Post-correction logic for jurisdiction
                if item["type"] == "jurisdiction":
                    reason = item.get("reason", "").lower()
                    if "county" in url or "county" in company.lower():
                        item["value"] = "County / Regional"
                    elif item["value"] != "County / Regional" and any(kw in reason for kw in [
                        "regional", "region", "gulf coast", "tri-state", "multi-county", "county",
                        "greater", "metro", "only agency in the region", "multiple counties"]):
                        item["value"] = "County / Regional"

                # Post-correction logic for organization_type
                if ".gov" in url and item["type"] == "organization_type":
                    item["value"] = "Government"

                validated_items.append(item)

            return validated_items

        except json.JSONDecodeError:
            print(f"⚠️ JSON Parsing Error! Raw Response: {response_content}")
            return []
        except openai.APIError as e:
            print(f"❌ OpenAI API Error: {e}")
            return []
        except Exception as e:
            print(f"❌ Unexpected Error processing OpenAI response: {e}")
            print(f"Raw response causing error: {response_content if 'response_content' in locals() else 'N/A'}")
            return []

    def clean_and_structure_contact_info(self, agency_name: str, agency_url: str, raw_items: List[Dict[str, Any]]) -> Optional[ContactDetail]:
        if not raw_items:
            print("No raw items to clean.")
            return ContactDetail(AgencyName=agency_name, AgencyUrl=agency_url, InformationPending=True)

        raw_items_json = json.dumps(raw_items, indent=2)

        system_message = {
            "role": "system",
            "content": "You are a strict JSON extractor and data cleaner. Follow the instructions exactly and output only valid JSON that matches the target schema."
        }

        user_message = {
            "role": "user",
            "content": f"""You are provided with a raw JSON list of contact information items extracted from the website for "{agency_name}" ({agency_url}). 
            Your task is to clean, validate, deduplicate, and structure this information into a single final JSON object that EXACTLY matches the target schema below.
            
            Target Schema:
            ```json
            {self.target_schema}
            ```

            Raw JSON List:
            ```json
            {raw_items_json}
            ```

            Cleaning and Structuring Rules:
            1. Set AgencyName to "{agency_name}" and AgencyUrl to "{agency_url}".
            2. Aggregate all information and remove exact duplicates within each list (for the agency and each person).
            3. Validate emails: include only valid email addresses.
            4. Validate phones: ensure numbers contain only digits; convert valid numbers to integers and discard invalid entries.
            5. Validate and format addresses:
                - Identify valid mailing (PO Box) or physical (street) addresses.
                - Discard entries that are purely numeric or are URLs.
                - Combine split address fragments into a single complete address. (e.g., "P.O. Box 432" and "Brewton, AL 36427" should be combined into "P.O. Box 432, Brewton, AL 36427").
                - Normalize addresses by removing extra whitespace and standardizing common abbreviations using standard postal formats. (e.g., St to Street, Ave./Ave to Avenue) using standard postal formats.
                - If multiple normalized addresses refer to the same location, keep only the most complete version.
            6. Group information by person_name; create one object per unique person in the ContactPerson list.
            7. Assign emails, phones, addresses, and roles correctly to the associated person or to the general agency lists if no person is indicated.
            8. For Socials, include URLs from facebook.com, x.com, twitter.com, instagram.com, or linkedin.com only; ignore all others.
            9. Determine Jurisdiction: Look for raw item where "type" == "jurisdiction". 
                - If "value" is "City" or "County / Regional", use that. 
                - If multiple conflicting values appear, choose the most frequent; if still ambiguous, set "Other". 
                - If no such raw item exists, set to "Other".
            10. Determine Organization Type: 
                - If agency_url is a .gov domain, or the raw data/HTML repeatedly uses government keywords (e.g. “Department”, “Commission”, “City of…”, “County of…”), set to "Government". 
                - Else if the site describes an independent council, mentions membership, donations, grants, sponsors (aside from government), or is on a non-.gov domain, set to "Non-profit". 
                - Otherwise set to "Other".
            11. Output ONLY a single JSON object (target schema) that exactly matches the target schema with no extra text or explanation.
            """
        }

        return self._process_openai_response(agency_name, agency_url, [system_message, user_message])

    def add_facebook_contact_info(self, agency_name: str, agency_url: str, raw_items: List[Dict[str, Any]], contact_detail: ContactDetail) -> Optional[ContactDetail]:
        if not raw_items:
            return contact_detail

        raw_items_json = json.dumps(raw_items, indent=2)
        current_detail = json.dumps(asdict(contact_detail), default=pydantic_json_encoder, indent=2)

        system_message = {
            "role": "system",
        "content": "You are a strict JSON data merger and cleaner. Follow the instructions exactly and output only a valid JSON object matching the target schema."
        }

        user_message = {
            "role": "user",
            "content": f"""You are provided with a raw JSON list of contact information extracted from Facebook and the current contact details for "{agency_name}" ({agency_url}). 
            Your task is to update the current contact details by merging in the new Facebook data while cleaning, validating, deduplicating and structure this information into a single final JSON object that EXACTLY matches the target schema below.
            
            Target Schema:
            ```json
            {self.target_schema}
            ```

            Raw JSON List from Facebook:
            ```json
            {raw_items_json}
            ```

            Current Contact Detail:
            ```json
            {current_detail}
            ```

            Rules for Updating Contact Detail:
            1. Use the current contact details as the starting point.
            2. Aggregate the new Facebook data with the existing details and remove exact duplicates (both at the agency level and within each person’s data).
            3. Validate emails by including only those in a correct format.
            4. Validate phone numbers by ensuring they contain only digits; convert valid numbers to integers and discard any invalid entries.
            5. Validate and format addresses:
                - Identify valid mailing (PO Box) or physical (street) addresses.
                - Discard entries that are purely numeric or are URLs.
                - Combine split address fragments into a single complete address. (e.g., "P.O. Box 432" and "Brewton, AL 36427" should be combined into "P.O. Box 432, Brewton, AL 36427").
                - Normalize addresses by removing extra whitespace and standardizing common abbreviations using standard postal formats. (e.g., St to Street, Ave./Ave to Avenue) using standard postal formats.
                - If multiple normalized addresses refer to the same location, keep only the most complete version.
            6. Never remove valid existing data.
            7. Add only new and unique Facebook data that does not duplicate what already exists.
            8. Output ONLY a single JSON object that exactly matches the target schema with no additional text or explanation.
            9. Do not change Jurisdiction and OrganizationType fields.
            """
        }

        return self._process_openai_response(agency_name, agency_url, [system_message, user_message])

    def normalize_contact_details(self, contact: ContactDetail):
        contact_detail = json.dumps(asdict(contact), default=pydantic_json_encoder, indent=2)
        system_message = {
            "role": "system",
            "content": f"""
                    You are a JSON-oriented ContactDetail assistant.  You will receive a single ContactDetail JSON object matching this Pydantic schema:
                ```json
                {self.target_schema}
                ```
                Your task is to
                
                1. **Clean all text fields** by removing control characters (ASCII codes 0-31 and 127-159), replacing them with appropriate spaces or removing them entirely. This includes names, addresses, and all other text fields. 
                
                2. **Select exactly one** email and one phone to remain in the **root-level** `Email` and `Phone` arrays, using this priority:
                    a. **Generic organizational**
                       - **Email**: any root‐level address matching `/^(info|contact|support|admin|hello|office)@/i`.
                       - **Phone**: any root‐level number **not** referenced by any `ContactPerson` entry.
                    b. **Mid-level roles**
                       - Scan `ContactPerson` entries whose `ContactRole` contains `engagement`, `manager`, `administrator`, `coordinator`, 'communication' or `program`.
                    c. **Top-level roles**
                       - Scan roles for `Executive Director`, `Director`, `Head`, `President`, or `CEO`.
                    d. **Last resort**
                       - The very first email/phone found (search root then persons in order).

                **Important:**
                - **Preserve** every other field exactly (including **all** `MailingAddress`, `PhysicalAddress`, `Socials`, and `ContactPerson` data).
                - **Do not remove or modify** any addresses or other properties except to clean control characters.
                - **Formatting**: lowercase all emails; strip non-digits from phones and return as integers.
                - **Output**: only the complete ContactDetail JSON with the root `Email` and `Phone` arrays reduced to your chosen values.
                """
        }

        user_message = {
            "role": "user",
            "content": f"""Here is the ContactDetail JSON to normalize:
            ```json
            {contact_detail}
            ```
            Clean all text fields by removing control characters, and apply the selection logic above. Return only the resulting ContactDetail object, where the root-level `Email` list contains your chosen email and the root-level `Phone` list contains your chosen phone, with every other field unchanged except for the removal of control characters."
            """
        }

        try:
            completion = self.client.chat.completions.create(
                model="gpt-4o-mini",  # Or a more powerful model if needed for complexity
                messages=[system_message, user_message],
                temperature=0,
                stop=["\n\n"],
                response_format={"type": "json_object"}
            )

            response_content = completion.choices[0].message.content.strip()
            if not response_content:
                print("⚠️ OpenAI cleaning step returned an empty response.")
                return None

            cleaned_data = json.loads(response_content)
            contact_person_data = cleaned_data.get('ContactPerson', [])
            reconstructed_persons = []

            for person_data in contact_person_data:
                contact_person = ContactPerson(
                    ContactName=person_data.get('ContactName', ''),
                    ContactRole=person_data.get('ContactRole', ''),
                    Email=person_data.get('Email', []),
                    Phone=person_data.get('Phone', []),
                    MailingAddress=person_data.get('MailingAddress', []),
                    PhysicalAddress=person_data.get('PhysicalAddress', []),
                    Socials=[social for social in person_data.get('Socials', []) if
                             any(keyword in social.lower() for keyword in self.social_keywords)]
                )
                reconstructed_persons.append(contact_person)

            agency_contact = ContactDetail(
                AgencyName=cleaned_data.get('AgencyName', contact.AgencyName),  # Use provided name as fallback
                AgencyUrl=cleaned_data.get('AgencyUrl', contact.AgencyUrl),  # Use provided URL as fallback
                Email=cleaned_data.get('Email', []),
                Phone=cleaned_data.get('Phone', []),
                MailingAddress=cleaned_data.get('MailingAddress', []),
                PhysicalAddress=cleaned_data.get('PhysicalAddress', []),
                ContactPerson=reconstructed_persons,
                Socials=[social for social in cleaned_data.get('Socials', []) if
                         any(keyword in social.lower() for keyword in self.social_keywords)],
                InformationPending=contact.InformationPending,
                Jurisdiction=contact.Jurisdiction,
                OrganizationType=contact.OrganizationType,
            )

            if "City" in agency_contact.Jurisdiction:
                agency_contact.Jurisdiction = "City"
            elif "County" in agency_contact.Jurisdiction:
                agency_contact.Jurisdiction = "County / Regional"
            else:
                agency_contact.Jurisdiction = "Other"

            if "Non-profit" in agency_contact.OrganizationType:
                agency_contact.OrganizationType = "Non-profit"
            elif "Government" in agency_contact.OrganizationType:
                agency_contact.OrganizationType = "Government"
            else:
                agency_contact.OrganizationType = "Other"

            return agency_contact

        except json.JSONDecodeError as json_err:
            print(f"❌ Error decoding JSON response from OpenAI: {json_err}")
            print(f"Raw response content:\n{response_content}")
            return None
        except ValidationError as pydantic_err:
            print(f"❌ Pydantic validation error during object reconstruction: {pydantic_err}")
            print(f"Cleaned data that failed validation:\n{cleaned_data}")
            return None
        except Exception as e:
            print(f"❌ An unexpected error occurred during OpenAI cleaning: {e}")
            return None

    def _process_openai_response(self, agency_name: str, agency_url: str, message: list[dict[str, str]]) -> Optional[ContactDetail]:
        try:
            completion = self.client.chat.completions.create(
                model="gpt-4o-mini", # Or a more powerful model if needed for complexity
                messages=message,
                temperature=0,
                stop=["\n\n"],
                response_format={"type": "json_object"}
            )

            response_content = completion.choices[0].message.content.strip()
            if not response_content:
                print("⚠️ OpenAI cleaning step returned an empty response.")
                return None

            cleaned_data = json.loads(response_content)

            information_pending_flag = True
            # Check agency-level lists
            if cleaned_data.get('Email') and cleaned_data.get('Phone') and cleaned_data.get('Address'):
                information_pending_flag = False

            contact_person_data = cleaned_data.get('ContactPerson', [])
            reconstructed_persons = []

            for person_data in contact_person_data:
                mailing_address = []
                physical_address = []
                for address in person_data.get('Address', []):
                    if ('pobox' in re.sub(r'[^a-zA-Z0-9]', '', address).lower()):
                        mailing_address.append(address)
                    else:
                        physical_address.append(address)
                contact_person = ContactPerson(
                    ContactName=person_data.get('ContactName', ''),
                    ContactRole=person_data.get('ContactRole', ''),
                    Email=person_data.get('Email', []),
                    Phone=person_data.get('Phone', []),
                    MailingAddress=mailing_address,
                    PhysicalAddress=physical_address,
                    Socials=[social for social in person_data.get('Socials', []) if any(keyword in social.lower() for keyword in self.social_keywords)]
                )
                reconstructed_persons.append(contact_person)

            mailing_address = []
            physical_address = []
            for address in cleaned_data.get('Address', []):
                if 'pobox' in re.sub(r'[^a-zA-Z0-9]', '', address).lower():
                    mailing_address.append(address)
                else:
                    physical_address.append(address)

            agency_contact = ContactDetail(
                AgencyName=cleaned_data.get('AgencyName', agency_name), # Use provided name as fallback
                AgencyUrl=cleaned_data.get('AgencyUrl', agency_url), # Use provided URL as fallback
                Email=cleaned_data.get('Email', []),
                Phone=cleaned_data.get('Phone', []),
                MailingAddress=mailing_address,
                PhysicalAddress=physical_address,
                ContactPerson=reconstructed_persons,
                Socials=[social for social in cleaned_data.get('Socials', []) if any(keyword in social.lower() for keyword in self.social_keywords)],
                Jurisdiction=cleaned_data.get('Jurisdiction', 'Other'),
                OrganizationType=cleaned_data.get('OrganizationType', 'Other'),
                InformationPending=information_pending_flag # Use calculated flag
            )

            return agency_contact

        except json.JSONDecodeError as json_err:
            print(f"❌ Error decoding JSON response from OpenAI: {json_err}")
            print(f"Raw response content:\n{response_content}")
            return None
        except ValidationError as pydantic_err:
            print(f"❌ Pydantic validation error during object reconstruction: {pydantic_err}")
            print(f"Cleaned data that failed validation:\n{cleaned_data}")
            return None
        except Exception as e:
            print(f"❌ An unexpected error occurred during OpenAI cleaning: {e}")
            return None